import generatePurchaseOrderPDF from "@/components/purchase/PurchaseOrderPDF";

// Dummy email share handler. Replace with actual email API integration.
export async function shareOrderByEmail(recipient, order) {
  // Generate PDF as Blob
  const pdfBlob = await generatePurchaseOrderPDF(order, { returnBlob: true });

  // Send to backend API for email delivery
  const formData = new FormData();
  formData.append("file", pdfBlob, `PO_${order.po_number || order.id}.pdf`);
  formData.append("email", recipient);

  const response = await fetch("/api/send-po-email", {
    method: "POST",
    body: formData,
  });

  if (!response.ok) {
    throw new Error("Failed to send PO via Email");
  }
  return true;
}
