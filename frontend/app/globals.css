@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

/* Bubble Transitions */
#bubbles {
  --size: 250vw;
  z-index: 9998;
  pointer-events: none;
}

.bubbles__first,
.bubbles__second {
  position: fixed;
  top: 0;
  left: 50%;
  transform: translate(-50%, 100%);
  width: var(--size);
  height: var(--size);
  border-radius: var(--size);
  animation-timing-function: ease-in-out;
}

#bubbles.show .bubbles__first {
  animation: bubble-move 1200ms;
}

#bubbles.show .bubbles__second {
  animation: bubble-second-move 1200ms;
}

#bubbles.hide .bubbles__first,
#bubbles.hide .bubbles__second {
  opacity: 0;
  transition: opacity 400ms;
}

@keyframes bubble-move {
  20% { border-radius: var(--size); }
  50%, 100% { transform: translate(-50%, 0); border-radius: 0; }
}

@keyframes bubble-second-move {
  30% { transform: translate(-50%, 100%); }
  50% { border-radius: var(--size); }
  100% { transform: translate(-50%, 0); border-radius: 0; }
}

/* Add smooth transitions */
* {
  transition: background-color 0.2s ease, opacity 0.2s ease;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}
.svgIcon {
  width: 24px;  /* Default size (larger than before) */
  height: 24px;
  transition: transform 0.2s ease-in-out;
}

.button:hover .svgIcon {
  transform: scale(1.2); /* Slightly bigger on hover */
}

/* app/globals.css */
.swal2-popup {
  @apply !rounded-2xl !border-2 !border-red-500/50 !bg-gray-900 !text-gray-100;
  animation: swal2-show 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55) !important;
}

@keyframes swal2-show {
  0% { transform: scale(0.7); opacity: 0; }
  80% { transform: scale(1.05); }
  100% { transform: scale(1); opacity: 1; }
}

.swal2-title {
  @apply !text-red-400 !text-2xl !font-bold;
}

.swal2-close {
  @apply !text-gray-400 hover:!text-red-500 !transition-colors;
}

@keyframes blink {
  50% { opacity: 0; }
}

.animate-blink {
  animation: blink 1s infinite;
}

.delay-75 {
  animation-delay: 100ms;
}

.delay-150 {
  animation-delay: 500ms;
}

@keyframes glitch {
  0% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
  100% { transform: translate(0); }
}

.glitch-effect {
  animation: glitch 1s infinite;
  text-shadow: 2px 2px #ff0000, -2px -2px #00ffff;
}