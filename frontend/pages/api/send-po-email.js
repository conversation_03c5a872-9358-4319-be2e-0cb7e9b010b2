import nodemailer from "nodemailer";

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Parse form data
    const data = await new Promise((resolve, reject) => {
      const form = require("formidable")();
      form.parse(req, (err, fields, files) => {
        if (err) reject(err);
        else resolve({ fields, files });
      });
    });
    const { email } = data.fields;
    const file = data.files.file;

    // Setup nodemailer
    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: email,
      subject: "Purchase Order",
      text: "Please find attached your Purchase Order.",
      attachments: [
        {
          filename: file.originalFilename,
          path: file.filepath,
        },
      ],
    });

    return res.status(200).json({ message: "PO sent via email" });
  } catch (err) {
    console.error("Email send error:", err);
    return res.status(500).json({ error: "Failed to send email" });
  }
}

export const config = {
  api: {
    bodyParser: false,
  },
};
